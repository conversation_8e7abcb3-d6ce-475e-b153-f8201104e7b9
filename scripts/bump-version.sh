#!/bin/bash

# Safe, smart version bumping script with build metadata for repeated builds

set -e

# Get latest semver tag
LATEST_TAG=$(git tag --list "v*" --sort=-version:refname | head -1)

if [ -z "$LATEST_TAG" ]; then
    echo "No existing tags found. Starting at v0.1.0"
    BASE_VERSION="v0.1.0"
    TAG_COMMIT=""
else
    echo "Latest tag: $LATEST_TAG"
    
    # Extract base version (strip -build if present)
    BASE_VERSION="${LATEST_TAG%%-build*}"
    TAG_COMMIT=$(git rev-list -n 1 "$LATEST_TAG")
fi

# Get current commit hash
CURRENT_COMMIT=$(git rev-parse HEAD)

# Strip 'v' prefix for version parsing
VERSION=${BASE_VERSION#v}

# Check if current commit matches latest tag
if [ "$CURRENT_COMMIT" == "$TAG_COMMIT" ]; then
    DATE=$(date +%Y%m%d)

    # Count how many build metadata tags already exist for this base version and date
    EXISTING_TAGS=$(git tag --list "${BASE_VERSION}-build.${DATE}.*")
    COUNT=$(echo "$EXISTING_TAGS" | wc -l)
    NEXT_BUILD_NUM=$((COUNT + 1))

    # Construct metadata tag
    NEW_TAG="${BASE_VERSION}-build.${DATE}.${NEXT_BUILD_NUM}"
    echo "🔁 Same commit as latest tag. Creating build metadata tag: $NEW_TAG"

    git tag "$NEW_TAG"
    git push origin "$NEW_TAG" 2>/dev/null || echo "⚠️ Warning: Could not push tag (may be running locally)"

    echo "✅ Tagged as: $NEW_TAG"
    exit 0
fi

# If commit is new, bump the patch version
IFS='.' read -r MAJOR MINOR PATCH <<< "$VERSION"
PATCH=$((PATCH + 1))
NEW_TAG="v${MAJOR}.${MINOR}.${PATCH}"

echo "🏷️ New commit. Creating new version tag: $NEW_TAG"
git tag "$NEW_TAG"
git push origin "$NEW_TAG" 2>/dev/null || echo "⚠️ Warning: Could not push tag (may be running locally)"

echo "✅ Version bumped to: $NEW_TAG"
