"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  ArrowLeft,
  Play,
  Pause,
  Edit,
  Trash2,
  Users,
  MessageSquare,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  RefreshCw,
  Smartphone,
} from "lucide-react"
import { BroadcastAPI, DevicesAPI } from "@/lib/services"
import {
  Broadcast,
  BroadcastRecipient,
  BroadcastStatus,
  BroadcastRecipientStatus,
} from "@/lib/repositories/broadcast/interface"
import { MobileChatPreview } from "@/components/broadcast/MobileChatPreview"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "../locales"

const statusColors = {
  [BroadcastStatus.DRAFT]: "bg-gray-100 text-gray-800",
  [BroadcastStatus.SCHEDULED]: "bg-blue-100 text-blue-800",
  [BroadcastStatus.SENDING]: "bg-yellow-100 text-yellow-800",
  [BroadcastStatus.COMPLETED]: "bg-green-100 text-green-800",
  [BroadcastStatus.FAILED]: "bg-red-100 text-red-800",
  [BroadcastStatus.CANCELLED]: "bg-gray-100 text-gray-800",
}

const recipientStatusIcons = {
  [BroadcastRecipientStatus.PENDING]: (
    <Clock className="h-4 w-4 text-gray-500" />
  ),
  [BroadcastRecipientStatus.SENT]: (
    <CheckCircle className="h-4 w-4 text-green-500" />
  ),
  [BroadcastRecipientStatus.DELIVERED]: (
    <CheckCircle className="h-4 w-4 text-green-600" />
  ),
  [BroadcastRecipientStatus.FAILED]: (
    <XCircle className="h-4 w-4 text-red-500" />
  ),
}

export default function BroadcastDetailPage() {
  const { t } = useLocalization("broadcast", locales)
  const router = useRouter()
  const params = useParams()
  const broadcastId = params.id as string

  const [broadcast, setBroadcast] = useState<Broadcast | null>(null)
  const [recipients, setRecipients] = useState<BroadcastRecipient[]>([])
  const [device, setDevice] = useState<any>(null)
  const [progress, setProgress] = useState({
    totalTargets: 0,
    sentCount: 0,
    failedCount: 0,
    pendingCount: 0,
    successRate: 0,
    status: BroadcastStatus.DRAFT,
  })
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)

  const fetchBroadcastData = async () => {
    try {
      setLoading(true)
      const [broadcastResponse, recipientsResponse, progressResponse] =
        await Promise.all([
          BroadcastAPI.Detail(broadcastId).request(),
          BroadcastAPI.Recipients(broadcastId, {
            page: 1,
            per_page: 100,
            sort: [],
          }).request(),
          BroadcastAPI.Progress(broadcastId).request(),
        ])

      setBroadcast(broadcastResponse)
      setRecipients(recipientsResponse.items || [])
      setProgress({
        ...progressResponse,
        status: progressResponse.status as BroadcastStatus,
      })

      // Fetch device information if broadcast has deviceId
      if (broadcastResponse.deviceId) {
        try {
          const deviceResponse = await DevicesAPI.Detail(
            broadcastResponse.deviceId,
          ).request()
          setDevice(deviceResponse)
        } catch (error) {
          console.error(t("detail.errors.fetch_device"), error)
          setDevice(null)
        }
      }
    } catch (error) {
      console.error(t("detail.errors.fetch_data"), error)
    } finally {
      setLoading(false)
    }
  }

  const refreshProgress = async () => {
    try {
      setRefreshing(true)
      const [recipientsResponse, progressResponse] = await Promise.all([
        BroadcastAPI.Recipients(broadcastId, {
          page: 1,
          per_page: 100,
          sort: [],
        }).request(),
        BroadcastAPI.Progress(broadcastId).request(),
      ])

      setRecipients(recipientsResponse.items || [])
      setProgress({
        ...progressResponse,
        status: progressResponse.status as BroadcastStatus,
      })
    } catch (error) {
      console.error(t("detail.errors.refresh_progress"), error)
    } finally {
      setRefreshing(false)
    }
  }

  useEffect(() => {
    if (broadcastId) {
      fetchBroadcastData()
    }
  }, [broadcastId])

  // Real-time updates for active broadcasts
  useEffect(() => {
    if (!progress || !["PENDING", "IN_PROGRESS"].includes(progress.status)) {
      return
    }

    const interval = setInterval(() => {
      // Only refresh progress and recipients, not the full broadcast data
      Promise.all([
        BroadcastAPI.Recipients(broadcastId, {
          page: 1,
          per_page: 100,
          sort: [],
        }).request(),
        BroadcastAPI.Progress(broadcastId).request(),
      ])
        .then(([recipientsResponse, progressResponse]) => {
          setRecipients(recipientsResponse.items || [])
          setProgress({
            ...progressResponse,
            status: progressResponse.status as BroadcastStatus,
          })
        })

        .catch((error) => {
          console.error(t("detail.errors.refresh_status_console"), error)
        })
    }, 5000) // Update every 5 seconds

    return () => clearInterval(interval)
  }, [broadcastId, progress?.status])

  // Auto-refresh progress for active broadcasts
  useEffect(() => {
    if (broadcast?.status === BroadcastStatus.SENDING) {
      const interval = setInterval(refreshProgress, 5000) // Refresh every 5 seconds
      return () => clearInterval(interval)
    }
  }, [broadcast?.status, broadcastId])

  const handleStartBroadcast = async () => {
    try {
      await BroadcastAPI.Start(broadcastId).request()
      fetchBroadcastData()
    } catch (error) {
      console.error(t("detail.errors.start_failed"), error)
    }
  }

  const handleCancelBroadcast = async () => {
    if (!confirm(t("detail.confirmations.cancel"))) return

    try {
      await BroadcastAPI.Cancel(broadcastId).request()
      fetchBroadcastData()
    } catch (error) {
      console.error(t("detail.errors.cancel_failed"), error)
    }
  }

  const handleDelete = async () => {
    if (!confirm(t("detail.confirmations.delete"))) return

    try {
      await BroadcastAPI.Delete(broadcastId).request()
      router.push("/broadcast")
    } catch (error) {
      console.error(t("detail.errors.delete_failed"), error)
    }
  }

  const formatDate = (date: string | Date) => {
    return new Date(date).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  // Calculate actual recipient count based on broadcast selection logic
  const getActualRecipientCount = () => {
    if (!broadcast) return 0

    // Always use the totalTargets from the broadcast as it's calculated during creation
    // This includes the proper logic for tag-based selection and exclusions
    return broadcast.totalTargets || 0
  }

  // Get recipient count display with fallback
  const getDisplayRecipientCount = () => {
    const actualCount = getActualRecipientCount()
    const progressCount = progress?.totalTargets || 0

    // Use the higher of the two values, or show both if they differ significantly
    if (actualCount !== progressCount && actualCount > 0) {
      return `${actualCount} (${progressCount} in progress)`
    }

    return actualCount || progressCount || 0
  }

  if (loading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </div>
      </div>
    )
  }

  if (!broadcast) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center py-12">
          <p className="text-gray-500">{t("detail.not_found")}</p>
          <Button onClick={() => router.push("/broadcast")} className="mt-4">
            {t("detail.back_to_broadcasts")}
          </Button>
        </div>
      </div>
    )
  }

  const progressPercentage =
    progress.totalTargets > 0
      ? ((progress.sentCount + progress.failedCount) / progress.totalTargets) *
      100
      : 0

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold">{broadcast.title}</h1>
            <p className="text-gray-600">{t("detail.title")}</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={refreshProgress}
            disabled={refreshing}
          >
            <RefreshCw
              className={`h-4 w-4 ${refreshing ? "animate-spin" : ""}`}
            />
          </Button>
          {broadcast.status === BroadcastStatus.DRAFT && (
            <>
              <Button
                variant="outline"
                onClick={() => router.push(`/broadcast/${broadcastId}/edit`)}
              >
                <Edit className="h-4 w-4 mr-2" />
                {t("detail.edit")}
              </Button>
              <Button onClick={handleStartBroadcast}>
                <Play className="h-4 w-4 mr-2" />
                {t("detail.start_broadcast")}
              </Button>
            </>
          )}
          {broadcast.status === BroadcastStatus.SENDING && (
            <Button variant="destructive" onClick={handleCancelBroadcast}>
              <Pause className="h-4 w-4 mr-2" />
              {t("detail.cancel")}
            </Button>
          )}
          <Button variant="destructive" onClick={handleDelete}>
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Broadcast Info */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Left side - Broadcast details */}
        <div className="lg:col-span-3 space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MessageSquare className="h-5 w-5" />
                  {t("detail.sections.message")}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm whitespace-pre-wrap">
                  {broadcast.message}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  {t("detail.sections.recipients")}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Recipient Selection Info */}
                  <div className="p-3 bg-blue-50 rounded-lg">
                    <div className="text-sm font-medium text-blue-900 mb-2">
                      {t("detail.recipient_selection.title")}
                    </div>
                    <div className="space-y-1 text-sm text-blue-800">
                      {broadcast.recipientTags &&
                        broadcast.recipientTags.length > 0 && (
                          <div>
                            <span className="font-medium">
                              {t("detail.recipient_selection.tags")}:
                            </span>{" "}
                            <div className="flex flex-wrap gap-1 mt-1">
                              {broadcast.recipientTags.map((tag) => (
                                <Badge
                                  key={tag}
                                  variant="secondary"
                                  className="text-xs"
                                >
                                  {tag}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        )}
                      {broadcast.excludedRecipientIds &&
                        broadcast.excludedRecipientIds.length > 0 && (
                          <div>
                            <span className="font-medium">
                              {t("detail.recipient_selection.excluded")}:
                            </span>{" "}
                            {broadcast.excludedRecipientIds.length}{" "}
                            {t("detail.recipient_selection.contacts")}
                          </div>
                        )}
                      {broadcast.manualSelectedTargetRecipients &&
                        broadcast.manualSelectedTargetRecipients.length > 0 && (
                          <div>
                            <span className="font-medium">
                              Manual Contacts:
                            </span>{" "}
                            {broadcast.manualSelectedTargetRecipients.length}{" "}
                            contacts
                          </div>
                        )}
                    </div>
                  </div>

                  {/* Stats */}
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>{t("detail.stats.total_targets")}</span>
                      <span className="font-medium">
                        {getDisplayRecipientCount()}
                      </span>
                    </div>
                    <div className="flex justify-between text-green-600">
                      <span>{t("detail.stats.sent")}</span>
                      <span className="font-medium">{progress.sentCount}</span>
                    </div>
                    <div className="flex justify-between text-red-600">
                      <span>{t("detail.stats.failed")}</span>
                      <span className="font-medium">
                        {progress.failedCount}
                      </span>
                    </div>
                    <div className="flex justify-between text-gray-600">
                      <span>{t("detail.stats.pending")}</span>
                      <span className="font-medium">
                        {progress.pendingCount}
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertCircle className="h-5 w-5" />
                {t("detail.sections.status")}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Badge className={statusColors[broadcast.status]}>
                  {t(`status.${broadcast.status}`)}
                </Badge>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>{t("detail.stats.progress")}</span>
                    <span>{Math.round(progressPercentage)}%</span>
                  </div>
                  <Progress value={progressPercentage} className="h-2" />
                </div>
                <div className="text-sm text-gray-600">
                  <div>
                    {t("detail.stats.created")}{" "}
                    {formatDate(broadcast.createdAt)}
                  </div>
                  {broadcast.startedAt && (
                    <div>
                      {t("detail.stats.started")}{" "}
                      {formatDate(broadcast.startedAt)}
                    </div>
                  )}
                  {broadcast.completedAt && (
                    <div>
                      {t("detail.stats.completed")}{" "}
                      {formatDate(broadcast.completedAt)}
                    </div>
                  )}
                  {device && (
                    <div className="mt-2 pt-2 border-t">
                      <div className="font-medium text-gray-800">
                        {t("detail.stats.device")}
                      </div>
                      <div>
                        {device.name} ({device.sessionId})
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Right side - Mobile Preview */}
        <div className="lg:col-span-1">
          <div className="sticky top-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Smartphone className="h-5 w-5" />
                  {t("detail.sections.mobile_preview")}
                </CardTitle>
                <CardDescription>
                  {t("detail.sections.mobile_preview_description")}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <MobileChatPreview
                  message={broadcast.message}
                  senderName={
                    broadcast.title || t("form.preview.sender_fallback")
                  }
                  timestamp={
                    broadcast.createdAt
                      ? new Date(broadcast.createdAt)
                      : new Date()
                  }
                />
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {broadcast.status !== BroadcastStatus.DRAFT && (
        <>
          <Card>
            <CardHeader>
              <CardTitle>
                {t("detail.recipients_table.title", {
                  count: recipients.length,
                })}
              </CardTitle>
              <CardDescription>
                {t("detail.recipients_table.description")}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {recipients.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  {t("detail.recipients_table.no_recipients")}
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>
                        {t("detail.recipients_table.headers.name")}
                      </TableHead>
                      <TableHead>
                        {t("detail.recipients_table.headers.contact")}
                      </TableHead>
                      <TableHead>
                        {t("detail.recipients_table.headers.tags")}
                      </TableHead>
                      <TableHead>
                        {t("detail.recipients_table.headers.status")}
                      </TableHead>
                      <TableHead>
                        {t("detail.recipients_table.headers.sent_at")}
                      </TableHead>
                      <TableHead>
                        {t("detail.recipients_table.headers.error")}
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {recipients.map((recipient, index) => (
                      <TableRow key={recipient.contactId || index}>
                        <TableCell className="font-medium">
                          <div>
                            <div className="font-medium">
                              {recipient.name ||
                                recipient.contactId ||
                                t("detail.recipients_table.unknown_contact")}
                            </div>
                            {recipient.contactId &&
                              recipient.contactId !== recipient.name && (
                                <div className="text-xs text-gray-500">
                                  {t("detail.recipients_table.id_prefix")}{" "}
                                  {recipient.contactId}
                                </div>
                              )}
                          </div>
                        </TableCell>
                        <TableCell className="text-sm text-gray-600">
                          <div className="space-y-1">
                            {recipient.phone && (
                              <div className="flex items-center gap-1">
                                <span className="text-xs text-gray-400">
                                  📱
                                </span>
                                {recipient.phone}
                              </div>
                            )}
                            {recipient.email && (
                              <div className="flex items-center gap-1">
                                <span className="text-xs text-gray-400">
                                  ✉️
                                </span>
                                {recipient.email}
                              </div>
                            )}
                            {!recipient.phone && !recipient.email && (
                              <span className="text-gray-400">
                                {recipient.contactId}
                              </span>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-wrap gap-1">
                            {/* Note: Tags would need to be added to BroadcastRecipient interface */}
                            {broadcast.recipientTags &&
                              broadcast.recipientTags.length > 0 ? (
                              broadcast.recipientTags.map((tag) => (
                                <Badge
                                  key={tag}
                                  variant="secondary"
                                  className="text-xs"
                                >
                                  {tag}
                                </Badge>
                              ))
                            ) : (
                              <span className="text-xs text-gray-400">
                                {t("detail.recipients_table.no_tags_short")}
                              </span>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {recipientStatusIcons[recipient.status] || (
                              <Clock className="h-4 w-4 text-gray-500" />
                            )}
                            <span className="capitalize text-sm">
                              {t(`recipient_status.${recipient.status}`)}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            {recipient.sentAt
                              ? formatDate(recipient.sentAt)
                              : "-"}
                            {recipient.deliveredAt && (
                              <div className="text-xs text-green-600">
                                {t("detail.recipients_table.delivered_prefix")}{" "}
                                {formatDate(recipient.deliveredAt)}
                              </div>
                            )}
                            {recipient.failedAt && (
                              <div className="text-xs text-red-600">
                                {t("detail.recipients_table.failed_prefix")}{" "}
                                {formatDate(recipient.failedAt)}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          {recipient.errorMessage && (
                            <div className="text-red-600 text-sm max-w-xs">
                              <div
                                className="truncate"
                                title={recipient.errorMessage}
                              >
                                {recipient.errorMessage}
                              </div>
                            </div>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </>
      )}
    </div>
  )
}
