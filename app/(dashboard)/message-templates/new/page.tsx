"use client"

import { DataEditorPage, DataEditorConfig } from "@/components/crud-page"
import { MessageTemplatesAPI } from "@/lib/services/messageTemplatesApi"
import { useLocalization } from "@/localization/functions/client"
import { messageTemplatesLocales } from "../locales"

export default function NewMessageTemplatePage() {
  const { t } = useLocalization("message-templates", messageTemplatesLocales)

  const messageTemplateEditorConfig: DataEditorConfig = {
    title: t("title"),
    subtitle: t("subtitle"),

    fields: [
      {
        name: "title",
        label: t("fields.name.label"),
        type: "text",
        placeholder: t("fields.name.placeholder"),
        description: `${t("fields.name.description")}\n\n${t("fields.name.examples")}`,
        validation: {
          required: true,
          minLength: 2,
          maxLength: 100,
        },
        group: "basic",
      },
      {
        name: "query",
        label: t("fields.query.label"),
        type: "text",
        placeholder: t("fields.query.placeholder"),
        description: `${t("fields.query.description")}\n\n${t("fields.query.examples")}`,
        validation: {
          required: true,
          maxLength: 100,
        },
        group: "basic",
      },
      {
        name: "template",
        label: t("fields.content.label"),
        type: "textarea",
        placeholder: t("fields.content.placeholder"),
        description: `${t("fields.content.description")}\n\n${t("fields.content.examples")}`,
        rows: 6,
        validation: {
          required: true,
          minLength: 5,
        },
        group: "content",
      },
      {
        name: "variables",
        label: t("fields.variables.label"),
        type: "text",
        placeholder: t("fields.variables.placeholder"),
        description: `${t("fields.variables.description")}\n\n${t("fields.variables.examples")}`,
        group: "content",
      },
      {
        name: "tags",
        label: t("fields.tags.label"),
        type: "text",
        placeholder: t("fields.tags.placeholder"),
        description: `${t("fields.tags.description")}\n\n${t("fields.tags.examples")}`,
        group: "meta",
      },
      {
        name: "description",
        label: t("fields.description.label"),
        type: "textarea",
        placeholder: t("fields.description.placeholder"),
        description: `${t("fields.description.description")}\n\n${t("fields.description.examples")}`,
        rows: 3,
        group: "meta",
        validation: {
          maxLength: 500,
        },
      },
    ],

    sections: [
      {
        title: t("sections.basicInfo.name"),
        description: t("sections.basicInfo.description"),
        fields: ["title", "query"],
      },
      {
        title: t("sections.content.name"),
        description: t("sections.content.description"),
        fields: ["template", "variables"],
      },
      {
        title: t("sections.metadata.name"),
        description: t("sections.metadata.description"),
        fields: ["tags", "description"],
      },
    ],

    saveData: async (data: Record<string, any>, _isEdit: boolean) => {
      try {
        const transformedData = {
          title: data.title,
          query: data.query,
          template: data.template,
          variables: data.variables
            ? data.variables
              .split(",")
              .map((v: string) => v.trim())
              .filter(Boolean)
            : [],
          tags: data.tags
            ? data.tags
              .split(",")
              .map((v: string) => v.trim())
              .filter(Boolean)
            : [],
          description: data.description || "",
        }

        await MessageTemplatesAPI.Create(transformedData).request()
      } catch (error: any) {
        console.error("Error creating messageTemplate:", error)

        let errorMessage = "Failed to create message template"
        if (error?.response?.data?.messages?.length > 0) {
          errorMessage = error.response.data.messages[0]
        } else if (error?.message) {
          errorMessage = error.message
        }

        throw new Error(errorMessage)
      }
    },

    backRoute: "/message-templates",
    successRoute: "/message-templates",

    submitButtonText: t("buttons.create"),
    cancelButtonText: t("buttons.cancel"),
    showImagePreview: false,
  }

  return <DataEditorPage config={messageTemplateEditorConfig} />
}
