import assert from "assert"

export class ResponseWrapper<T> {
  public readonly status: "success" | "failed"
  public readonly data?: T
  public readonly error?: string[]
  public readonly errorCodes?: string[]
  public readonly messages: string[]

  constructor(
    status: "success" | "failed",
    data?: T,
    error?: string[],
    errorCodes?: string[],
    messages: string[] = [],
  ) {
    this.status = status
    this.data = data
    this.error = error
    this.errorCodes = errorCodes
    this.messages = messages

    // Assert success should NOT have error
    if (this.status === "success" && this.error) {
      throw new Error(
        `Invalid ResponseWrapper: status is "success" but error is present: ${JSON.stringify(this.error)}`
      )
    }

    // Assert failed MUST have error
    if (this.status === "failed" && !this.error) {
      throw new Error(
        `Invalid ResponseWrapper: status is "failed" but error is missing`
      )
    }
  }
}


export type SuccessResponse<T> = {
  status: "success"
  data: T
  messages: string[]
}

export type FailedResponse = {
  status: "failed"
  error: string[]
  errorCodes: string[]
}

export type _NewResponseWrapper<T> = SuccessResponse<T> | FailedResponse
export function NewResponseWrapper<T>(value: _NewResponseWrapper<T>): _NewResponseWrapper<T> {
  return value
}

export class ResponseBuilder {
  static success<T>(data: T, messages: string[] = []): SuccessResponse<T> {
    return {
      status: "success",
      data,
      messages,
    }
  }

  static fail(error: string[], errorCodes: string[]): FailedResponse {
    return {
      status: "failed",
      error,
      errorCodes,
    }
  }
}
