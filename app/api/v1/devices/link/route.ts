import { buildSessionContext } from "@/app/api/sharedFunction"
import { providers } from "@/lib/providers"
import { SessionContext } from "@/lib/repositories/auth/types"
import { getBusinessLogics } from "@/lib/repositories/businessLogics"
import { DeviceStatus } from "@/lib/repositories/devices/interface"
import { driver } from "@/lib/repositories/LiveMongoDriver"
import { ResponseBuilder, ResponseWrapper } from "@/lib/types/responseWrapper"
import { NextRequest, NextResponse } from "next/server"
import z from "zod"

const schema = z.object({
  name: z.string().optional(),
})

export async function POST(req: NextRequest) {
  try {
    const { devicesBusinessLogic } = getBusinessLogics()
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const body = await req.json()
    const validationResult = schema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        new ResponseWrapper(
          "failed",
          undefined,
          validationResult.error.errors.map((e) => e.message),
          ["VALIDATION_FAILED"],
        ),
        { status: 400 },
      )
    }

    const provider = providers[process.env.WHATSAPP_PROVIDER!]
    const generateId = () => Math.random().toString(36).substring(2, 11)
    const result = await provider.createSession(
      context.user.id + "-" + generateId(),
    )
    await saveSessionToContext(result, context)

    const device = await devicesBusinessLogic.create(
      {
        sessionId: result,
        name: validationResult.data.name || context.user.name,
        platform: "phone",
        status: DeviceStatus.SCANNING,
        providerName: provider.name,
      },
      context,
    )

    return NextResponse.json(
      new ResponseWrapper("success", { sessionId: result, device }),
      { status: 200 },
    )
  } catch (error) {
    console.error("Link device route error:", error)
    return NextResponse.json(
      ResponseBuilder.fail(
        ["api.error.internal_server_error"],
        [
          "ERROR_INTERNAL_SERVER_ERROR",
        ]),
      { status: 500 },
    )
  }
}

export async function saveSessionToContext(
  sessionId: string,
  context: SessionContext,
) {
  const sessionToContextTable = driver.getCollection("session_to_context")
  sessionToContextTable.createIndex({ session: 1 }, { unique: true })
  await sessionToContextTable.insertOne({
    session: sessionId,
    user: context.user,
    organization: context.organization,
  })
}

export async function getContextFromSession(sessionId: string) {
  const sessionToContextTable = driver.getCollection("session_to_context")
  const doc = await sessionToContextTable.findOne({ session: sessionId })
  if (!doc) return null

  return {
    user: doc.user,
    organization: doc.organization,
  }
}
