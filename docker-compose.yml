version: "3.8"

services:
  cs-ai-app:
    image: ${CS_AI_APP_IMAGE:-cs-ai-app:latest}
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "${DOCKER_EXPOSED_PORT:-3000}:3000"
    env_file:
      - .env
    environment:
      - NODE_ENV=production
      - PORT=3000
      - HOSTNAME=0.0.0.0
    restart: unless-stopped
    networks:
      - cs-ai-network
    volumes:
      # Optional: Mount logs directory if your app writes logs
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    depends_on:
      - rabbitmq

  rabbitmq:
    image: rabbitmq:3.12-management
    container_name: cs-ai-rabbitmq
    ports:
      - "5672:5672" # AMQP port
      - "15672:15672" # Management UI port
    environment:
      - RABBITMQ_DEFAULT_USER=admin
      - RABBITMQ_DEFAULT_PASS=admin123
      - RABBITMQ_DEFAULT_VHOST=/
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - cs-ai-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  broadcast-worker:
    build:
      context: ./app_microservices/python
      dockerfile: Dockerfile
    env_file:
      - .env
    environment:
      - PYTHONUNBUFFERED=1
    networks:
      - cs-ai-network
    restart: unless-stopped
    depends_on:
      - rabbitmq
      - cs-ai-app
    volumes:
      - ./app_microservices/python:/app

networks:
  cs-ai-network:
    name: cs-ai-network
    external: true

volumes:
  rabbitmq_data:
