import { NextRequest, NextResponse } from "next/server"
import { getBusinessLogics } from "@/lib/repositories/businessLogics"
import { implHandleGetBroadcastRecipients } from "../../impl"
import { ERROR_CODES } from "@/app/api/error_codes"
import { buildSessionContext } from "@/app/api/sharedFunction"

// GET /api/v1/broadcast/[id]/recipients - Get broadcast recipients
export async function GET(
  req: NextRequest,
  routeContext: { params: Promise<{ id: string }> },
) {
  const { broadcastBusinessLogic, broadcastRecipientBusinessLogic } = getBusinessLogics()
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { id } = await routeContext.params
    const result = await implHandleGetBroadcastRecipients(
      id,
      broadcastBusinessLogic,
      broadcastRecipientBusinessLogic,
      context,
    )
    return NextResponse.json(result.body, { status: result.status })
  } catch (error: any) {
    console.error("Error in GET /api/v1/broadcast/[id]/recipients:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: undefined,
        messages: [error.message || "Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR],
      },
      { status: 500 },
    )
  }
}
