import { assert } from "console"

export class ResponseWrapper<T> {
  public readonly status: "success" | "failed"
  public readonly data?: T
  public readonly error?: string[]
  public readonly errorCodes?: string[]
  public readonly messages: string[]

  constructor(
    status: "success" | "failed",
    data?: T,
    error?: string[],
    errorCodes?: string[],
    messages: string[] = [],
  ) {
    this.status = status
    this.data = data
    this.error = error
    this.errorCodes = errorCodes
    this.messages = messages
    assert(this.status === "success" || !this.error, "Success response must not have error")
    assert(this.status === "failed" || this.error, "Failed response must have error")
  }
}


export type SuccessResponse<T> = {
  status: "success"
  data: T
  messages: string[]
}

export type FailedResponse = {
  status: "failed"
  error: string[]
  errorCodes: string[]
}

export type NewResponseWrapper<T> = SuccessResponse<T> | FailedResponse

export class ResponseBuilder {
  static success<T>(data: T, messages: string[] = []): SuccessResponse<T> {
    return {
      status: "success",
      data,
      messages,
    }
  }

  static fail(error: string[], errorCodes: string[]): FailedResponse {
    return {
      status: "failed",
      error,
      errorCodes,
    }
  }
}
