import { NextRequest, NextResponse } from "next/server"
import { isMessageQueueHealthy } from "@/lib/queue"

export async function GET(req: NextRequest) {
  try {
    const isHealthy = await isMessageQueueHealthy()
    
    if (isHealthy) {
      return NextResponse.json({
        status: "healthy",
        message: "Message queue is connected and operational",
        timestamp: new Date().toISOString(),
      })
    } else {
      return NextResponse.json(
        {
          status: "unhealthy",
          message: "Message queue is not connected",
          timestamp: new Date().toISOString(),
        },
        { status: 503 }
      )
    }
  } catch (error: any) {
    console.error("Message queue health check failed:", error)
    return NextResponse.json(
      {
        status: "error",
        message: "Failed to check message queue health",
        error: error.message,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    )
  }
}
