# RabbitMQ Makefile (DEV & STG Support with Separate Recipes)

.PHONY: help \
        network_dev network_stg \
        up up_stg up-rabbitmq up-worker \
        up-rabbitmq_stg up-worker_stg \
        down down_stg \
        logs logs_stg logs-rmq logs-worker logs-rmq_stg logs-worker_stg \
        restart restart_stg \
        clean clean_stg \
        status status_stg \
        health health_stg \
        shell-rmq shell-worker \
        shell-rmq_stg shell-worker_stg \
        dev-setup dev-teardown \
        stg-setup stg-teardown

# ========== HELP ==========

help:
	@echo "Available commands:"
	@echo ""
	@echo "  DEV environment:"
	@echo "    network_dev       - Create dev network"
	@echo "    up                - Start all dev services"
	@echo "    up-rabbitmq       - Start only RabbitMQ (dev)"
	@echo "    up-worker         - Start only broadcast worker (dev)"
	@echo "    down              - Stop dev services"
	@echo "    logs              - Show logs (dev)"
	@echo "    restart           - Restart dev services"
	@echo "    clean             - Clean dev containers and volumes"
	@echo "    status            - Show dev status"
	@echo "    health            - Check dev service health"
	@echo "    shell-rmq         - Open shell in RabbitMQ container (dev)"
	@echo "    shell-worker      - Open shell in worker container (dev)"
	@echo "    dev-setup         - Dev quick setup"
	@echo "    dev-teardown      - Dev cleanup"
	@echo ""
	@echo "  STG environment:"
	@echo "    network_stg       - Create stg network"
	@echo "    up_stg            - Start all stg services"
	@echo "    up-rabbitmq_stg   - Start only RabbitMQ (stg)"
	@echo "    up-worker_stg     - Start only broadcast worker (stg)"
	@echo "    down_stg          - Stop stg services"
	@echo "    logs_stg          - Show logs (stg)"
	@echo "    restart_stg       - Restart stg services"
	@echo "    clean_stg         - Clean stg containers and volumes"
	@echo "    status_stg        - Show stg status"
	@echo "    health_stg        - Check stg service health"
	@echo "    shell-rmq_stg     - Open shell in RabbitMQ container (stg)"
	@echo "    shell-worker_stg  - Open shell in worker container (stg)"
	@echo "    stg-setup         - Staging quick setup"
	@echo "    stg-teardown      - Staging cleanup"

# ========== NETWORKS ==========

network_dev:
	@docker network inspect cs-ai-network >/dev/null 2>&1 || \
	docker network create cs-ai-network

network_stg:
	@docker network inspect cs-ai-network-stg >/dev/null 2>&1 || \
	docker network create cs-ai-network-stg

# ========== COMPOSE WRAPPERS ==========

define dc_dev
	docker compose -f docker-compose.override.yml -f docker-compose.yml --env-file ../../.env $(1)
endef

define dc_stg
	docker compose -f docker-compose.override.yml -f docker-compose.stg.yml --env-file ../../.env.stg $(1)
endef

# ========== START/STOP ==========

up: network_dev
	$(call dc_dev,up -d)

up_stg: network_stg
	$(call dc_stg,up -d)

up-rabbitmq: network_dev
	$(call dc_dev,up -d rabbitmq)

up-worker: network_dev
	$(call dc_dev,up -d broadcast-worker)

up-rabbitmq_stg: network_stg
	$(call dc_stg,up -d rabbitmq)

up-worker_stg: network_stg
	$(call dc_stg,up -d broadcast-worker)

down:
	$(call dc_dev,down)

down_stg:
	$(call dc_stg,down)

restart:
	$(call dc_dev,restart)

restart_stg:
	$(call dc_stg,restart)

clean:
	$(call dc_dev,down -v)
	@docker volume rm cs-ai-rabbitmq-data 2>/dev/null || true

clean_stg:
	$(call dc_stg,down -v)
	@docker volume rm cs-ai-rabbitmq-data 2>/dev/null || true

# ========== LOGGING ==========

logs:
	$(call dc_dev,logs -f)

logs_stg:
	$(call dc_stg,logs -f)

logs-rmq:
	$(call dc_dev,logs -f rabbitmq)

logs-worker:
	$(call dc_dev,logs -f broadcast-worker)

logs-rmq_stg:
	$(call dc_stg,logs -f rabbitmq)

logs-worker_stg:
	$(call dc_stg,logs -f broadcast-worker)

# ========== STATUS ==========

status:
	$(call dc_dev,ps)

status_stg:
	$(call dc_stg,ps)

# ========== HEALTH ==========

health:
	@echo "=== RabbitMQ Health (DEV) ==="
	@docker compose -f docker-compose.yml --env-file ../../.env exec rabbitmq rabbitmq-diagnostics ping || echo "RabbitMQ not healthy"
	@echo ""
	@echo "=== Container Status ==="
	$(call dc_dev,ps)

health_stg:
	@echo "=== RabbitMQ Health (STG) ==="
	@docker compose -f docker-compose.stg.yml --env-file ../../.env.stg exec rabbitmq rabbitmq-diagnostics ping || echo "RabbitMQ not healthy"
	@echo ""
	@echo "=== Container Status ==="
	$(call dc_stg,ps)

# ========== SHELL ACCESS ==========

shell-rmq:
	$(call dc_dev,exec rabbitmq bash)

shell-worker:
	$(call dc_dev,exec broadcast-worker bash)

shell-rmq_stg:
	$(call dc_stg,exec rabbitmq bash)

shell-worker_stg:
	$(call dc_stg,exec broadcast-worker bash)

# ========== SHORTCUTS ==========

dev-setup: network_dev up
	@echo "✅ Dev environment is ready!"
	@echo "🔗 RabbitMQ Management UI: http://localhost:15672"
	@echo "🚪 AMQP Port: localhost:5672"

dev-teardown: clean
	@echo "🧹 Dev environment cleaned!"

stg-setup: network_stg up_stg
	@echo "✅ STG environment is ready!"
	@echo "🔗 RabbitMQ Management UI: http://localhost:15673"
	@echo "🚪 AMQP Port: localhost:5673"

stg-teardown: clean_stg
	@echo "🧹 STG environment cleaned!"
