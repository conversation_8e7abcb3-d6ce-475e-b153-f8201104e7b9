import { createError } from "@/lib/utils/common"
import { SessionContext } from "../auth/types"
import { BroadcastSender } from "./BroadcastSender"
import { BroadcastDBRepository } from "./DBRepository"
import {
  Broadcast,
  BroadcastBusinessLogicInterface,
  BroadcastCreateInput,
  BroadcastQueryParams,
  BroadcastRecipientBusinessLogicInterface,
  BroadcastStats,
  BroadcastStatus,
  BroadcastUpdateInput
} from "./interface"

export class BroadcastBusinessLogic implements BroadcastBusinessLogicInterface {
  constructor(
    private db: BroadcastDBRepository,
    private broadcastSender: BroadcastSender,
    private broadcastRecipientBusinessLogic: BroadcastRecipientBusinessLogicInterface
  ) { }
  private validateId(id: string): void {
    if (!id || typeof id !== "string" || id.trim() === "") {
      throw createError("Invalid ID provided", "INVALID_ID")
    }
  }

  private buildContextFilters(context: SessionContext) {
    const filters = []

    if (context.organization?.id) {
      filters.push({ field: "organizationId", value: context.organization.id })
    } else {
      filters.push({ field: "createdBy", value: context.user.id })
    }

    return filters
  }

  private trimCreateInput(data: BroadcastCreateInput): BroadcastCreateInput {
    if (
      !data.title ||
      typeof data.title !== "string" ||
      data.title.trim() === ""
    ) {
      throw createError("Title is required", "INVALID_TITLE")
    }

    if (
      !data.message ||
      typeof data.message !== "string" ||
      data.message.trim() === ""
    ) {
      throw createError("Message is required", "INVALID_MESSAGE")
    }

    if (
      !data.deviceId ||
      typeof data.deviceId !== "string" ||
      data.deviceId.trim() === ""
    ) {
      throw createError("Device is required", "INVALID_DEVICE")
    }

    return {
      ...data,
      title: data.title.trim(),
      message: data.message.trim(),
      deviceId: data.deviceId.trim(),
    }
  }

  async create(
    data: BroadcastCreateInput,
    context: SessionContext,
  ): Promise<Broadcast> {
    const trimmedData = this.trimCreateInput(data)

    const dataWithContext = {
      ...trimmedData,
      status: BroadcastStatus.DRAFT,
      createdBy: context.user.id,
      organizationId: context.organization?.id,
    }

    const broadcast = await this.db.create(dataWithContext)

    let finalBroadcast = await this.fillBroadcastInformation(broadcast, context)

    const updated = await this.db.update(broadcast.id, finalBroadcast)
    if (updated) {
      finalBroadcast = updated
    }
    return finalBroadcast
  }

  async duplicate(
    id: string,
    context: SessionContext,
    newTitle?: string,
  ): Promise<Broadcast> {
    this.validateId(id)

    // Get the original broadcast
    const originalBroadcast = await this.getById(id, context)
    if (!originalBroadcast) {
      throw createError("Broadcast not found", "BROADCAST_NOT_FOUND")
    }

    // Create duplicate data
    const duplicateData: BroadcastCreateInput = {
      title: newTitle || `${originalBroadcast.title} (Copy)`,
      message: originalBroadcast.message,
      manualSelectedTargetRecipients:
        originalBroadcast.manualSelectedTargetRecipients,
      deviceId: originalBroadcast.deviceId,
      recipientTags: originalBroadcast.recipientTags,
      excludedRecipientIds: originalBroadcast.excludedRecipientIds,
    }

    // Create the duplicate broadcast
    return await this.create(duplicateData, context)
  }

  async getById(
    id: string,
    context: SessionContext,
    includeDeleted = false,
  ): Promise<Broadcast | null> {
    this.validateId(id)

    const contextFilters = this.buildContextFilters(context)
    const filters = [{ field: "id", value: id }, ...contextFilters]

    if (!includeDeleted) {
      filters.push({ field: "deletedAt", value: undefined as any })
    }

    const result = await this.db.getAll({ filters, limit: 1 })
    return result.items.length > 0 ? result.items[0] : null
  }

  async getAll(
    params: BroadcastQueryParams,
    context: SessionContext,
  ): Promise<{ items: Broadcast[]; total: number }> {
    const contextFilters = this.buildContextFilters(context)

    const queryParams = {
      ...params,
      filters: [...(params.filters || []), ...contextFilters],
    }

    return this.db.getAll(queryParams)
  }

  async update(
    id: string,
    data: BroadcastUpdateInput,
    context: SessionContext,
  ): Promise<Broadcast | null> {
    this.validateId(id)

    if (!data || Object.keys(data).length === 0) {
      throw createError("No data provided for update", "INVALID_UPDATE_DATA")
    }

    const existing = await this.db.getById(id)
    if (!existing) {
      throw createError("Broadcast not found", "NOT_FOUND")
    }

    if (existing.status !== BroadcastStatus.DRAFT) {
      throw createError(
        "Cannot update broadcast that is not in draft status",
        "INVALID_STATUS",
      )
    }

    const updateData: any = {
      ...data,
      updatedBy: context.user.id,
      updatedAt: new Date(),
    }

    const finalUpdatedData = await this.fillBroadcastInformation(
      updateData,
      context,
    )

    return this.db.update(id, finalUpdatedData)
  }

  private async fillBroadcastInformation(
    broadcast: Broadcast,
    context: SessionContext,
  ): Promise<Broadcast> {
    const totalRecipientsCount =
      await this.broadcastRecipientBusinessLogic.countEstimatedTotalRecipientForBroadcastData(
        broadcast,
        context,
      )
    broadcast.totalTargets = totalRecipientsCount
    return broadcast
  }

  async delete(
    id: string,
    context: SessionContext,
    hardDelete = false,
  ): Promise<boolean> {
    this.validateId(id)

    const contextFilters = this.buildContextFilters(context)
    const existingResult = await this.db.getAll({
      filters: [{ field: "id", value: id }, ...contextFilters],
    })

    if (existingResult.items.length === 0) {
      throw createError("Broadcast not found", "NOT_FOUND")
    }

    return this.db.delete(id, hardDelete)
  }

  async startBroadcast(id: string, context: SessionContext): Promise<boolean> {
    const broadcast = await this.getById(id, context)
    if (!broadcast) {
      throw createError("Broadcast not found", "NOT_FOUND")
    }

    if (broadcast.status !== BroadcastStatus.DRAFT) {
      throw createError(
        "Can only start broadcasts in draft status",
        "INVALID_STATUS",
      )
    }

    await this.db.update(id, {
      status: BroadcastStatus.SENDING,
      startedAt: new Date(),
      updatedBy: context.user.id,
    })

    // Trigger actual broadcast sending logic
    await this.performBroadcastSending(id, context)

    return true
  }

  async cancelBroadcast(id: string, context: SessionContext): Promise<boolean> {
    const broadcast = await this.getById(id, context)
    if (!broadcast) {
      throw createError("Broadcast not found", "NOT_FOUND")
    }

    if (broadcast.status !== BroadcastStatus.SENDING) {
      throw createError(
        "Can only cancel broadcasts that are currently sending",
        "INVALID_STATUS",
      )
    }

    await this.db.update(id, {
      status: BroadcastStatus.CANCELLED,
      updatedBy: context.user.id,
    })

    return true
  }

  async scheduleBroadcast(
    id: string,
    scheduledAt: Date,
    context: SessionContext,
  ): Promise<boolean> {
    const broadcast = await this.getById(id, context)
    if (!broadcast) {
      throw createError("Broadcast not found", "NOT_FOUND")
    }

    if (broadcast.status !== BroadcastStatus.DRAFT) {
      throw createError(
        "Can only schedule broadcasts in draft status",
        "INVALID_STATUS",
      )
    }

    await this.db.update(id, {
      status: BroadcastStatus.SCHEDULED,
      scheduledAt,
      updatedBy: context.user.id,
    })

    return true
  }

  private async performBroadcastSending(
    id: string,
    context: SessionContext,
  ): Promise<boolean> {
    const broadcast = await this.getById(id, context)
    if (!broadcast) {
      throw createError("Broadcast not found", "NOT_FOUND")
    }

    if (broadcast.status !== BroadcastStatus.SENDING) {
      throw createError(
        "Can only send broadcasts that are in sending status",
        "INVALID_STATUS",
      )
    }

    // Use the BroadcastSender to handle the actual sending
    const result = await this.broadcastSender.sendBroadcast(broadcast, context)

    // Update broadcast with results
    const pendingCount =
      broadcast.totalTargets - result.sentCount - result.failedCount
    const isCompleted = pendingCount === 0

    await this.db.update(id, {
      sentCount: result.sentCount,
      failedCount: result.failedCount,
      pendingCount,
      status: isCompleted ? BroadcastStatus.COMPLETED : BroadcastStatus.SENDING,
      completedAt: isCompleted ? new Date() : undefined,
      updatedBy: context.user.id,
    })

    return true
  }

  async getStats(context: SessionContext): Promise<BroadcastStats> {
    const contextFilters = this.buildContextFilters(context)

    // Get total broadcasts
    const totalResult = await this.db.getAll({
      filters: [...contextFilters, { field: "deletedAt", value: null }],
    })

    // Get active broadcasts (sending)
    const activeResult = await this.db.getAll({
      filters: [
        ...contextFilters,
        { field: "status", value: BroadcastStatus.SENDING },
      ],
    })

    // Get completed broadcasts
    const completedResult = await this.db.getAll({
      filters: [
        ...contextFilters,
        { field: "status", value: BroadcastStatus.COMPLETED },
      ],
    })

    // Calculate total messages sent
    const totalMessagesSent = totalResult.items.reduce(
      (sum, broadcast) => sum + broadcast.sentCount,
      0,
    )

    // Calculate average success rate
    const broadcastsWithMessages = totalResult.items.filter(
      (b) => b.totalTargets > 0,
    )
    const averageSuccessRate =
      broadcastsWithMessages.length > 0
        ? (broadcastsWithMessages.reduce(
          (sum, b) => sum + b.sentCount / b.totalTargets,
          0,
        ) /
          broadcastsWithMessages.length) *
        100
        : 0

    return {
      totalBroadcasts: totalResult.total,
      activeBroadcasts: activeResult.total,
      completedBroadcasts: completedResult.total,
      totalMessagesSent,
      averageSuccessRate: Math.round(averageSuccessRate),
      recentActivity: [], // TODO: Implement recent activity tracking
    }
  }

  async getBroadcastProgress(
    id: string,
    context: SessionContext,
  ): Promise<{
    totalTargets: number
    sentCount: number
    failedCount: number
    pendingCount: number
    successRate: number
    status: BroadcastStatus
  }> {
    const broadcast = await this.getById(id, context)
    if (!broadcast) {
      throw createError("Broadcast not found", "NOT_FOUND")
    }

    const successRate =
      broadcast.totalTargets > 0
        ? Math.round((broadcast.sentCount / broadcast.totalTargets) * 100)
        : 0

    return {
      totalTargets: broadcast.totalTargets,
      sentCount: broadcast.sentCount,
      failedCount: broadcast.failedCount,
      pendingCount: broadcast.pendingCount,
      successRate,
      status: broadcast.status,
    }
  }
}
