## 🚀 How to Deploy

Follow these simple steps to deploy the project using Docker Compose on your VPS.

### 📦 1. Copy Project Files to Your VPS

Make sure you copy the following files to your VPS:

- `docker-compose.yml`
- `.env` (you will create this next)
- (OPTIONAL) Any required service files (e.g., `nginx.conf`, etc.)

You can use `scp`, `rsync`, or any deployment tool to do this.

### ⚙️ 2. Configure Environment Variables

Create a `.env` file on the VPS based on the provided example:

```bash
cp .env.example .env
```

Then open `.env` and **update the values** according to your environment (e.g., database credentials, secrets, ports, etc.).

### 🐳 3. Start the Services

Run the following command to start everything in detached mode:

```bash
docker compose up -d
```

This will:

- Pull all required images
- Start containers in the background
- Use environment variables from `.env`

### ✅ 4. Verify Deployment

Check the container status with:

```bash
docker compose ps
```

To view logs:

```bash
docker compose logs -f
```
