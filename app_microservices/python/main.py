#!/usr/bin/env python3
import os
import json
import time
import signal
import sys
import requests
import pika
from dotenv import load_dotenv
from typing import Dict, Any
import logging

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class BroadcastWorker:
    def __init__(self):
        self.rabbitmq_url = os.getenv('RABBITMQ_URL', '')

        if not self.rabbitmq_url:
            logger.error("RABBITMQ_URL is not set in the environment.")
            sys.exit(1)

        self.connection = None
        self.channel = None
        self.should_stop = False

        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def _signal_handler(self, signum, frame):
        logger.info(f"Received signal {signum}, shutting down gracefully...")
        self.should_stop = True

    def connect_to_rabbitmq(self):
        max_retries = 10
        retry_delay = 5

        for attempt in range(max_retries):
            try:
                logger.info(f"Connecting to RabbitMQ (attempt {attempt + 1}/{max_retries})")
                parameters = pika.URLParameters(self.rabbitmq_url)
                self.connection = pika.BlockingConnection(parameters)
                self.channel = self.connection.channel()
                self.channel.basic_qos(prefetch_count=1)
                logger.info("Successfully connected to RabbitMQ")
                return True
            except Exception as e:
                logger.error(f"RabbitMQ connection failed: {e}")
                if attempt < max_retries - 1:
                    logger.info(f"Retrying in {retry_delay} seconds...")
                    time.sleep(retry_delay)
                else:
                    logger.error("Max retries reached. Exiting.")
                    return False

    def process_message(self, ch, method, properties, body):
        try:
            message = json.loads(body.decode('utf-8'))
            logger.info(f"Received message: {message.get('type', 'unknown')}")

            if message.get('type') != 'broadcast.send':
                logger.error(f"Unsupported message type: {message.get('type')}")
                ch.basic_nack(delivery_tag=method.delivery_tag, requeue=False)
                return

            callback_url = message.get('callbackUrl')
            token = message.get('internalSystemToken')

            if not callback_url:
                logger.error("Missing 'callbackUrl' in message.")
                ch.basic_nack(delivery_tag=method.delivery_tag, requeue=False)
                return

            if not token:
                logger.error("Missing 'internalSystemToken' in message.")
                ch.basic_nack(delivery_tag=method.delivery_tag, requeue=False)
                return

            headers = {
                'Content-Type': 'application/json',
                'X-Internal-System-Token': token
            }

            logger.info(f"Sending POST to {callback_url}")

            response = requests.post(
                callback_url,
                json=message,
                headers=headers,
                timeout=30
            )

            logger.info(f"Response {response.status_code}: {response.text}")

            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    logger.info("Message processed successfully.")
                    ch.basic_ack(delivery_tag=method.delivery_tag)
                else:
                    logger.error(f"Callback returned error: {result.get('error')}")
                    ch.basic_nack(delivery_tag=method.delivery_tag, requeue=False)
            else:
                logger.error(f"Request failed with status {response.status_code}")
                ch.basic_nack(delivery_tag=method.delivery_tag, requeue=True)

        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in message: {e}")
            ch.basic_nack(delivery_tag=method.delivery_tag, requeue=False)

        except requests.exceptions.RequestException as e:
            logger.error(f"HTTP request error: {e}")
            ch.basic_nack(delivery_tag=method.delivery_tag, requeue=True)

        except Exception as e:
            logger.error(f"Unexpected error: {e}")
            ch.basic_nack(delivery_tag=method.delivery_tag, requeue=True)

    def start_consuming(self):
        try:
            logger.info("Listening to 'broadcast.send' queue")
            self.channel.basic_consume(
                queue='broadcast.send',
                on_message_callback=self.process_message
            )

            logger.info("Waiting for messages... Press CTRL+C to stop.")
            while not self.should_stop:
                self.connection.process_data_events(time_limit=1)

        except Exception as e:
            logger.error(f"Error while consuming messages: {e}")
        finally:
            self.cleanup()

    def cleanup(self):
        try:
            if self.channel and self.channel.is_open:
                self.channel.stop_consuming()
                self.channel.close()
            if self.connection and self.connection.is_open:
                self.connection.close()
            logger.info("Graceful shutdown complete.")
        except Exception as e:
            logger.error(f"Cleanup error: {e}")

    def run(self):
        logger.info("Broadcast Worker starting...")
        logger.info("Note: Each message must include 'callbackUrl' and 'internalSystemToken'")

        if not self.connect_to_rabbitmq():
            sys.exit(1)

        self.start_consuming()

def main():
    worker = BroadcastWorker()
    worker.run()

if __name__ == "__main__":
    main()
