NEXT_PUBLIC_APP_BASE_URL=https://yourdomain.com

SUPPORTED_LANGUAGES=en,id,ja,ar
DEFAULT_LANGUAGE=id

WAHA_API_URL=https://your-waha-instance.waha.my.id
WAHA_API_KEY=your_waha_api_key_here


WHATSAPP_PROVIDER=waha

PUSHER_APP_ID=your_pusher_app_id_here
PUSHER_KEY=your_pusher_key_here
PUSHER_SECRET=your_pusher_secret_here
PUSHER_CLUSTER=ap1

WAHA_WEBHOOK_URL=https://yourdomain.com/api/v1/functions/webhook
INTERNAL_SECRET_TOKEN=your_internal_secret_token_here

UPSTASH_REDIS_REST_URL=https://your-redis-instance.upstash.io
UPSTASH_REDIS_REST_TOKEN=your_upstash_redis_token_here

RESEND_API_KEY=your_resend_api_key_here
EMAIL_DOMAIN_SEND=yourdomain.com

MONGODB_URI=mongodb+srv://username:<EMAIL>/database?retryWrites=true&w=majority&appName=YourApp
REDIS_URL=https://your-redis-instance.upstash.io
REDIS_TOKEN=your_redis_token_here

JWT_REFRESH_SECRET=your_jwt_refresh_secret_here
JWT_SECRET=your_jwt_secret_here

AI_ENGINE_WEBHOOK_URL=https://your-n8n-instance.com/webhook/your-webhook-id
AI_ENGINE_CALLBACK_AI_EXECUTION=https://yourdomain.com/api/v1/ai/execution
AI_ENGINE_CALLBACK_AI_WORKFLOW_EXECUTION=https://yourdomain.com/api/v1/ai-workflow-executions/[id]/steps

PARSER_ENGINE_URL=https://your-n8n-instance.com/webhook/your-parser-webhook-id
PARSER_CALLBACK_URL=https://yourdomain.com/api/v1/knowledge-base/parser-callback

PINECONE_API_KEY=your_pinecone_api_key_here

CHAT_CLIENT_BASE_URL=http://localhost:5173

LOG_LEVEL=info                   
OVERRIDE_CONSOLE=true            
ENABLE_STACK_TRACE=false         
